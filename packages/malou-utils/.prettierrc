{"arrowParens": "always", "bracketSameLine": true, "bracketSpacing": true, "endOfLine": "lf", "importOrder": ["^@malou-io/(.*)$", "^[./]"], "importOrderCaseInsensitive": true, "importOrderSeparation": true, "importOrderSortSpecifiers": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "printWidth": 140, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "es5", "useTabs": false, "vueIndentScriptAndStyle": true}