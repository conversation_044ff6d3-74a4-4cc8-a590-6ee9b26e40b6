import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const postInsightJSONSchemaV2 = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'PostInsightV2',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        platformKey: {
            enum: Object.values(PlatformKey),
            default: PlatformKey.INSTAGRAM,
        },
        socialId: {
            type: 'string',
        },
        entityType: {
            enum: Object.values(PostInsightEntityType),
        },
        platformSocialId: {
            type: 'string',
        },
        lastFetchedAt: {
            type: 'string',
            format: 'date-time',
        },
        postSocialCreatedAt: {
            type: 'string',
            format: 'date-time',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: [
        '_id',
        'entityType',
        'platformKey',
        'platformSocialId',
        'socialId',
        'lastFetchedAt',
        'postSocialCreatedAt',
        'createdAt',
        'updatedAt',
    ],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
