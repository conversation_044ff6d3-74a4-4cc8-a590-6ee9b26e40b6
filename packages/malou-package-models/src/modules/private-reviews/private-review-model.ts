import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { privateReviewJSONSchema } from ':modules/private-reviews/private-review-schema';

const privateReviewSchema = createMongooseSchemaFromJSONSchema(privateReviewJSONSchema);

privateReviewSchema.virtual('client', {
    ref: 'Client',
    localField: 'clientId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('campaign', {
    ref: 'Campaign',
    localField: 'campaignId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('scan', {
    ref: 'Scans',
    localField: 'scanId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.virtual('translations', {
    ref: 'Translations',
    localField: 'translationsId',
    foreignField: '_id',
    justOne: true,
});

privateReviewSchema.index({ text: 'text', 'reviewer.displayName': 'text', 'comments.text': 'text' }); // index required by $search operator to search on review text: https://docs.mongodb.com/manual/text-search/
privateReviewSchema.index({ campaignId: 1 });
privateReviewSchema.index({ clientId: 1 });
privateReviewSchema.index({ scanId: 1 });
privateReviewSchema.index({ restaurantId: 1, socialSortDate: 1 });

privateReviewSchema.index({ publicBusinessId: 1 }, { unique: true, sparse: true });

export type IPrivateReview = FromSchema<
    typeof privateReviewJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const PrivateReviewModel = mongoose.model<IPrivateReview>(privateReviewJSONSchema.title, privateReviewSchema);
